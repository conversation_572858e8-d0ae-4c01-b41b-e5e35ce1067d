<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\PaymentCallbackController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\TestOrderController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::any('test', [TestController::class, 'test']);

Route::get('/', [HomeController::class, 'newIndex'])->name('home');
Route::get('/new', [HomeController::class, 'newIndex'])->name('home.new');

Route::get('articles/{id}', [HomeController::class, 'article'])->name('article');
Route::get('notifies/{id}', [HomeController::class, 'notify'])->name('notify');

Route::group(['prefix' => 'payment'], function () {
    Route::any('alipay/notify', [PaymentCallbackController::class, 'alipayNotify'])->name('payment.alipay.notify');
    Route::any('wechat/notify', [PaymentCallbackController::class, 'wechatNotify'])->name('payment.wechat.notify');

    // 新增商户充值支付回调
    Route::any('merchant/alipay/notify', [PaymentController::class, 'alipayNotify'])->name('merchant.payment.alipay.notify');
    Route::any('merchant/wechat/notify', [PaymentController::class, 'wechatNotify'])->name('merchant.payment.wechat.notify');
    Route::get('merchant/alipay/return', [PaymentController::class, 'alipayReturn'])->name('merchant.payment.alipay.return');
});

Route::get('map', function () {
    return view('test');
});

Route::get('order_map',[\App\Http\Controllers\MapController::class, 'orderMap'])->name('admin.order.map');
Route::get('rider_map', [\App\Http\Controllers\MapController::class, 'riderMap'])->name('admin.rider.map');

Route::get('site_region', function () {
    return view('siteregion');
})->name('site.regions');

Route::group(['prefix' => 'm'], function () {
    Route::get('share', [HomeController::class, 'share'])->name('m.share');
});

Route::post('open/callback', [HomeController::class, 'openCallback'])->name('open.callback');

Route::post('ipa/order/submit', [HomeController::class, 'orderSubmit'])->name('ipa.order_submit');

// 商家后台路由
Route::prefix('merchant')->name('merchant.')->group(function () {
    // 麦芽田授权登录
    Route::get('login/maiyatian', [App\Http\Controllers\Merchant\AuthController::class, 'showMytLoginForm'])->name('login.maiyatian')->middleware('force.relogin');

    // 不需要认证的路由
    Route::middleware('guest:merchant')->group(function () {
        // 登录路由
        Route::get('login', [App\Http\Controllers\Merchant\AuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [App\Http\Controllers\Merchant\AuthController::class, 'login']);
        Route::post('login/maiyatian', [App\Http\Controllers\Merchant\AuthController::class, 'loginWithMyt'])->name('login.maiyatian.post');


        // 注册路由
        Route::get('register', [App\Http\Controllers\Merchant\AuthController::class, 'showRegistrationForm'])->name('register');
        Route::post('register', [App\Http\Controllers\Merchant\AuthController::class, 'register']);

        // 忘记密码路由 - 使用手机验证码
        Route::get('password/reset', [App\Http\Controllers\Merchant\AuthController::class, 'showForgotPasswordForm'])->name('password.request');
        Route::post('password/reset/phone', [App\Http\Controllers\Merchant\AuthController::class, 'resetPasswordWithPhone'])->name('password.reset.phone');
        Route::post('send-verification-code', [App\Http\Controllers\Merchant\AuthController::class, 'sendVerificationCode'])->name('send.verification.code');

        // 图形验证码路由
        Route::get('captcha', [App\Http\Controllers\Merchant\AuthController::class, 'captcha'])->name('captcha');
        Route::get('refresh-captcha', [App\Http\Controllers\Merchant\AuthController::class, 'refreshCaptcha'])->name('refresh.captcha');
    });

    // 需要登录的路由
    Route::middleware('auth:merchant')->group(function () {
        // 首页
        Route::get('/', [App\Http\Controllers\Merchant\DashboardController::class, 'index'])->name('dashboard');
        Route::get('/dashboard', [App\Http\Controllers\Merchant\DashboardController::class, 'index']);
        Route::post('logout', [App\Http\Controllers\Merchant\AuthController::class, 'logout'])->name('logout');

        // 订单管理
        Route::get('orders', [App\Http\Controllers\Merchant\OrderController::class, 'index'])->name('orders');
        Route::get('orders/{id}', [App\Http\Controllers\Merchant\OrderController::class, 'show'])->name('orders.show');

        // 充值相关
        Route::get('recharge', [App\Http\Controllers\Merchant\RechargeController::class, 'index'])->name('recharge');
        Route::post('recharge', [App\Http\Controllers\Merchant\RechargeController::class, 'store']);
        Route::get('recharge/pay/{order_no}', [App\Http\Controllers\Merchant\RechargeController::class, 'pay'])->name('recharge.pay');
        Route::post('recharge/pay/{order_no}', [App\Http\Controllers\Merchant\RechargeController::class, 'doPay'])->name('recharge.do-pay');
        Route::get('recharge/qrcode/{order_no}', [App\Http\Controllers\Merchant\RechargeController::class, 'generateQrcode'])->name('recharge.qrcode');
        Route::get('recharge/check-status/{order_no}', [App\Http\Controllers\Merchant\RechargeController::class, 'checkStatus'])->name('recharge.check-status');
        Route::get('recharge/records', [App\Http\Controllers\Merchant\RechargeController::class, 'records'])->name('recharge.records');

        // 账户流水
        Route::get('account/logs', [App\Http\Controllers\Merchant\AccountController::class, 'logs'])->name('account.logs');
    });
});

Route::post('open/wrc/{command}', [\App\Http\Controllers\Api\MaiYaTianController::class, "wrcCallback"]);

// 骑手路径规划页面
Route::get('rider/route', function () {
    return view('rider_route');
});

// 获取测试订单数据
Route::get('api/v1/rider/orders', [TestOrderController::class, 'getOrders']);
