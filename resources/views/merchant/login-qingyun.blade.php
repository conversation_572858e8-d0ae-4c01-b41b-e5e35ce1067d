<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雨骑士商家管理系统 - 青云授权登录</title>
    <meta name="description" content="雨骑士商家管理系统，为商家提供专业的配送解决方案">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#d54b2f',
                        secondary: '#ff9500',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .bg-pattern {
            background-color: #f8f9fa;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d54b2f' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .gradient-primary {
            background: linear-gradient(135deg, #d54b2f 0%, #ff9500 100%);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-pattern min-h-screen">
    <div class="min-h-screen flex">
        <!-- 左侧背景区域 -->
        <div class="hidden md:flex md:w-1/2 gradient-bg relative overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
            <div class="absolute top-0 left-0 w-full h-full">
                <div class="absolute top-1/4 left-1/4 w-32 h-32 bg-white bg-opacity-10 rounded-full animate-pulse"></div>
                <div class="absolute top-3/4 right-1/4 w-24 h-24 bg-white bg-opacity-10 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                <div class="absolute bottom-1/4 left-1/3 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-pulse" style="animation-delay: 2s;"></div>
            </div>
            <div class="max-w-md p-8 z-10 animate-fade-in">
                <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-20 mb-8 invert">
                <h1 class="text-5xl font-bold text-white mb-6">雨骑士商家管理系统</h1>
                <p class="text-white text-xl mb-6 opacity-90">青云授权绑定</p>
                <p class="text-white text-md mb-10 opacity-80">将您的青云账号与雨骑士商家账号关联，享受便捷配送服务</p>
            </div>
        </div>
        
        <!-- 右侧登录表单 -->
        <div class="flex-1 flex items-center justify-center p-6 animate-fade-in">
            <div class="w-full max-w-md">
                <!-- 移动端Logo - 仅在小屏幕显示 -->
                <div class="md:hidden flex flex-col items-center mb-10 animate-slide-up">
                    <div class="w-24 h-24 rounded-full gradient-primary flex items-center justify-center mb-6 shadow-lg">
                        <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-14 invert">
                    </div>
                    <h1 class="text-2xl font-bold text-gray-800">青云授权绑定</h1>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-xl animate-slide-up">
                    <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">青云授权登录</h2>
                    
                    <div class="mb-6">
                        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-blue-700">
                                        请使用您的雨骑士商家账号登录，完成与青云的授权绑定
                                    </p>
                                    @if($shopName)
                                        <p class="text-xs text-blue-600 mt-1">
                                            门店：{{ $shopName }} ({{ $deviceType === 'app' ? '手机端' : 'PC端' }})
                                        </p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form action="{{ route('merchant.login.qingyun.post') }}" method="POST">
                        @csrf
                        <!-- 隐藏字段，传递青云授权参数 -->
                        <input type="hidden" name="shopId" value="{{ $shopId }}">
                        <input type="hidden" name="shopName" value="{{ $shopName }}">
                        <input type="hidden" name="deviceType" value="{{ $deviceType }}">
                        
                        <!-- 显示授权绑定错误 -->
                        @error('binding_error')
                            <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-red-700">
                                            {{ $message }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @enderror
                        
                        <div class="mb-6">
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                                    </svg>
                                </div>
                                <input type="text" id="phone" name="phone" value="{{ old('phone') }}" 
                                       class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200" 
                                       placeholder="请输入手机号" required>
                            </div>
                            @error('phone')
                                <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <input type="password" id="password" name="password" 
                                       class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200" 
                                       placeholder="请输入密码" required>
                            </div>
                            @error('password')
                                <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <a href="{{ route('merchant.register') }}" class="text-sm text-primary hover:text-secondary transition-colors duration-200">还没有账号？立即注册</a>
                        </div>
                        
                        <button type="submit" class="w-full gradient-primary text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-transform">授权绑定</button>
                    </form>
                </div>
                
                <div class="mt-8 text-center text-sm text-gray-500">
                    <p>© 2024 雨骑士. 保留所有权利.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
