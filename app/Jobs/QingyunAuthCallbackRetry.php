<?php

namespace App\Jobs;

use App\Services\QingyunService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class QingyunAuthCallbackRetry implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $shopId;
    protected int $retryCount;
    protected int $maxRetries;

    /**
     * Create a new job instance.
     *
     * @param string $shopId 青云门店ID
     * @param int $retryCount 当前重试次数
     * @param int $maxRetries 最大重试次数
     */
    public function __construct(string $shopId, int $retryCount = 0, int $maxRetries = 3)
    {
        $this->shopId = $shopId;
        $this->retryCount = $retryCount;
        $this->maxRetries = $maxRetries;
        
        // 设置队列延迟时间，重试次数越多延迟越长
        $this->delay = now()->addMinutes(pow(2, $retryCount)); // 指数退避：1分钟、2分钟、4分钟
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            Log::channel('qingyun')->info('青云授权回调重试任务开始', [
                'shop_id' => $this->shopId,
                'retry_count' => $this->retryCount,
                'max_retries' => $this->maxRetries
            ]);

            $qingyunService = new QingyunService();
            $result = $qingyunService->retryAuthResultCallback($this->shopId);

            if ($result['success']) {
                Log::channel('qingyun')->info('青云授权回调重试成功', [
                    'shop_id' => $this->shopId,
                    'retry_count' => $this->retryCount,
                    'result' => $result
                ]);
            } else {
                // 如果还没达到最大重试次数，继续重试
                if ($this->retryCount < $this->maxRetries) {
                    Log::channel('qingyun')->warning('青云授权回调重试失败，将继续重试', [
                        'shop_id' => $this->shopId,
                        'retry_count' => $this->retryCount,
                        'max_retries' => $this->maxRetries,
                        'result' => $result
                    ]);

                    // 派发下一次重试任务
                    dispatch(new self($this->shopId, $this->retryCount + 1, $this->maxRetries));
                } else {
                    Log::channel('qingyun')->error('青云授权回调重试达到最大次数，停止重试', [
                        'shop_id' => $this->shopId,
                        'retry_count' => $this->retryCount,
                        'max_retries' => $this->maxRetries,
                        'result' => $result
                    ]);
                }
            }

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云授权回调重试任务异常', [
                'shop_id' => $this->shopId,
                'retry_count' => $this->retryCount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 如果还没达到最大重试次数，继续重试
            if ($this->retryCount < $this->maxRetries) {
                dispatch(new self($this->shopId, $this->retryCount + 1, $this->maxRetries));
            }
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::channel('qingyun')->error('青云授权回调重试任务失败', [
            'shop_id' => $this->shopId,
            'retry_count' => $this->retryCount,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
