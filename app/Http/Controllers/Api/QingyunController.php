<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\QingyunService;
use App\Services\RateLimiterService;
use App\Models\O2oErrandOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class QingyunController extends Controller
{
    protected QingyunService $qingyunService;
    protected RateLimiterService $rateLimiterService;

    public function __construct(QingyunService $qingyunService, RateLimiterService $rateLimiterService)
    {
        $this->qingyunService = $qingyunService;
        $this->rateLimiterService = $rateLimiterService;
    }

    /**
     * 青云授权回调处理
     */
    public function authCallback(Request $request)
    {
        try {
            // 记录青云回调信息
            Log::channel('qingyun')->info('青云授权回调', $request->all());

            $shopId = $request->get('shopId');
            $shopName = $request->get('shopName');
            $deviceType = $request->get('deviceType', 'app');

            // 重定向到商家登录页面，并携带所有参数
            $merchantLoginUrl = route('merchant.login.qingyun');
            return redirect($merchantLoginUrl . '?' . http_build_query([
                'shopId' => $shopId,
                'shopName' => $shopName,
                'deviceType' => $deviceType,
                'source' => O2oErrandOrder::APP_KEY_QY
            ]));
        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云授权回调异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('merchant.login')->withErrors([
                'error' => '青云授权回调处理失败，请重试'
            ]);
        }
    }


    /**
     * 询价接口 - 获取预估配送费、配送时间等信息
     *
     * POST /api/qingyun/valuating
     * Content-Type: application/json
     */
    public function valuating(Request $request)
    {
        try {
            // 限流检查：30次/秒
            $rateLimitKey = 'qingyun:valuating:rate_limit';
            $maxRequests = 30; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('qingyun')->warning('青云询价请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }


            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required', // 平台订单号
                'recipientLng' => 'required', // 收件人经度
                'recipientLat' => 'required',// 收件人纬度
                'senderLng' => 'required', // 发件人经度
                'senderLat' => 'required',// 发件人纬度
                "carrierMerchantId"=>"required", // 承运商商户ID
            ], [
                'orderId.required' => '青云平台订单号不能为空',
                'recipientLng.required' => '收件人经度不能为空',
                'recipientLat.required' => '收件人纬度不能为空',
                'senderLng.required' => '发件人经度不能为空',
                'senderLat.required' => '发件人纬度不能为空',
                "carrierMerchantId.required"=>"门店id不能为空",
            ]);

            if ($validator->fails()) {

                return response()->json([
                    'code' => QingyunService::RESULT_PARAM_ERROR,
                    'message' => "缺少参数，或参数格式错误",
                    'data' => null
                ]);
            }

            // 检查发件人和收件人地址的运力覆盖
            $data = $request->all();

            // 转换经纬度格式（青云传入的是整数，需要转换为小数）
            $senderPoint = ["lng" => $data["senderLng"]/1000000, "lat" => $data["senderLat"]/1000000];
            $recipientPoint = ["lng" => $data["recipientLng"]/1000000, "lat" => $data["recipientLat"]/1000000];

            // 检查发件人地址运力覆盖
            $senderSite = app(\App\Services\CommonService::class)->getPointSite($senderPoint);
            if (!$senderSite) {
                return response()->json([
                    'code' => 106,
                    'message' => '运力紧张，无法创建订单',
                    'data' => null
                ]);
            }

            // 检查收件人地址运力覆盖
            $recipientSite = app(\App\Services\CommonService::class)->getPointSite($recipientPoint);
            if (!$recipientSite) {
                return response()->json([
                    'code' => 106,
                    'message' => '运力紧张，无法创建订单',
                    'data' => null
                ]);
            }

            $result = $this->qingyunService->valuatingWithOrderLookup($request->all());


            return response()->json($result);

        } catch (\Exception $e) {

            Log::channel('qingyun')->error('青云询价异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => QingyunService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 发单接口 - 创建配送订单
     *
     * POST /api/qingyun/send
     * Content-Type: application/json
     */
    public function send(Request $request)
    {
        try {
            // 限流检查：30次/秒
            $rateLimitKey = 'qingyun:send:rate_limit';
            $maxRequests = 30; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录青云发单请求信息
            Log::channel('qingyun')->info('青云发单请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required',
                'recipientLng' => 'required|integer',
                'recipientLat' => 'required|integer',
                'senderLng' => 'required|integer',
                'senderLat' => 'required|integer',
                "carrierMerchantId"=>"required", // 承运商商户ID
            ], [
                'orderId.required' => '青云平台订单号不能为空',
                'recipientLng.required' => '收件人经度不能为空',
                'recipientLat.required' => '收件人纬度不能为空',
                'senderLng.required' => '发件人经度不能为空',
                'senderLat.required' => '发件人纬度不能为空',
                "carrierMerchantId.required"=>"门店id不能为空",
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => QingyunService::RESULT_PARAM_ERROR,
                    'message' => "缺少参数，或参数格式错误",
                    'data' => null
                ]);
            }

            // 检查订单是否已存在
            $existingOrder = \App\Models\O2oErrandOrder::where('out_order_no', $request->input('orderId'))
                ->where('app_key', \App\Models\O2oErrandOrder::APP_KEY_QY)
                ->first();

            if ($existingOrder) {

                // 使用已存在的订单格式化返回数据
                $result = $this->qingyunService->formatOrderResultData($existingOrder);

                return response()->json([
                    'code' => QingyunService::RESULT_SUCCESS,
                    'message' => '成功',
                    'data' => $result
                ]);
            }

            // 检查发件人和收件人地址的运力覆盖
            $data = $request->all();

            // 转换经纬度格式（青云传入的是整数，需要转换为小数）
            $senderPoint = ["lng" => $data["senderLng"]/1000000, "lat" => $data["senderLat"]/1000000];
            $recipientPoint = ["lng" => $data["recipientLng"]/1000000, "lat" => $data["recipientLat"]/1000000];

            // 检查发件人地址运力覆盖
            $senderSite = app(\App\Services\CommonService::class)->getPointSite($senderPoint);
            if (!$senderSite) {
                return response()->json([
                    'code' => 106,
                    'message' => '运力紧张，无法创建订单',
                    'data' => null
                ]);
            }

            // 检查收件人地址运力覆盖
            $recipientSite = app(\App\Services\CommonService::class)->getPointSite($recipientPoint);
            if (!$recipientSite) {
                return response()->json([
                    'code' => 106,
                    'message' => '运力紧张，无法创建订单',
                    'data' => null
                ]);
            }

            // 调用服务处理发单逻辑
            $result = $this->qingyunService->send($request->all());


            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云发单异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => QingyunService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 取消订单接口
     *
     * POST /api/qingyun/cancel
     * Content-Type: application/json
     */
    public function cancel(Request $request)
    {
        try {
            // 限流检查：30次/秒
            $rateLimitKey = 'qingyun:cancel:rate_limit';
            $maxRequests = 30; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('qingyun')->warning('青云取消订单请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录青云取消订单请求信息
            Log::channel('qingyun')->info('青云取消订单请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required',
                'cancelReasonCode' => 'required',
                'cancelReasonDesc' => 'required',
            ], [
                'orderId.required' => '青云平台订单号不能为空',
                'cancelReasonCode.required' => '取消原因code不能为空',
                'cancelReasonDesc.required' => '取消原因说明不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('qingyun')->warning('青云取消订单参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => QingyunService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理取消订单逻辑
            $result = $this->qingyunService->cancelOrder($request->all());

            Log::channel('qingyun')->info('青云取消订单处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云取消订单异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => QingyunService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 订单详情接口
     *
     * POST /api/qingyun/order-detail
     * Content-Type: application/json
     */
    public function orderDetail(Request $request)
    {
        try {
            // 限流检查：30次/秒
            $rateLimitKey = 'qingyun:order_detail:rate_limit';
            $maxRequests = 30; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('qingyun')->warning('青云订单详情请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录青云订单详情请求信息
            Log::channel('qingyun')->info('青云订单详情请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required',
            ], [
                'orderId.required' => '青云平台订单号不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('qingyun')->warning('青云订单详情参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => QingyunService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理订单详情查询逻辑
            $result = $this->qingyunService->getOrderDetail($request->all());

            Log::channel('qingyun')->info('青云订单详情处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云订单详情异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => QingyunService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 骑手/司机经纬度查询接口
     *
     * POST /api/qingyun/rider-location
     * Content-Type: application/json
     */
    public function riderLocation(Request $request)
    {
        try {
            // 限流检查：30次/秒
            $rateLimitKey = 'qingyun:rider_location:rate_limit';
            $maxRequests = 30; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('qingyun')->warning('青云骑手位置查询请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录青云骑手位置查询请求信息
            Log::channel('qingyun')->info('青云骑手位置查询请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required',
            ], [
                'orderId.required' => '青云平台订单号不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('qingyun')->warning('青云骑手位置查询参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => QingyunService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理骑手位置查询逻辑
            $result = $this->qingyunService->getRiderLocation($request->all());

            Log::channel('qingyun')->info('青云骑手位置查询处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云骑手位置查询异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => QingyunService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 添加小费接口
     *
     * POST /api/qingyun/add-tip
     * Content-Type: application/json
     */
    public function addTip(Request $request)
    {
        try {
            // 限流检查：30次/秒
            $rateLimitKey = 'qingyun:add_tip:rate_limit';
            $maxRequests = 30; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('qingyun')->warning('青云添加小费请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录青云添加小费请求信息
            Log::channel('qingyun')->info('青云添加小费请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required|string',
                'tipFee' => 'required|numeric',
            ], [
                'orderId.required' => '青云平台订单号不能为空',
                'tipFee.required' => '小费金额不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('qingyun')->warning('青云添加小费参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => QingyunService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理添加小费逻辑
            $result = $this->qingyunService->addTip($request->all());

            Log::channel('qingyun')->info('青云添加小费处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云添加小费异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => QingyunService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

}
