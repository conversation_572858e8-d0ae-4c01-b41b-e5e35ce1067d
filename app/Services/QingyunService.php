<?php

namespace App\Services;

use App\Models\Common;
use App\Models\Merchant;
use App\Models\MerchantToken;
use App\Models\MerchantAccountLog;
use App\Models\O2oErrandOrder;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\UserAccount;
use App\Models\UserAccountFlow;
use App\Models\Site;
use App\Models\Pricing;
use App\Models\Region;
use App\Services\Amap\GaodeService;
use App\Services\UserService;
use App\Services\MapService;
use App\Services\CommonService;
use App\Services\O2oErrandOrderService;
use App\Exceptions\BusinessException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class QingyunService
{
    protected string $appKey;
    protected string $appSecret;

    // 青云订单来源Code
    const TRADE_ORDER_SOURCE_JINGDONG = 300;      // 京东
    const TRADE_ORDER_SOURCE_MEITUAN = 100;       // 美团
    const TRADE_ORDER_SOURCE_ELEME = 200;         // 饿了么
    const TRADE_ORDER_SOURCE_QINGYUN = 400;       // 青云自营
    const TRADE_ORDER_SOURCE_YOUZAN = 600;        // 有赞

    const TRADE_ORDER_SOURCE_DOUYIN = 700;        // 抖音
    const TRADE_ORDER_SOURCE_TAOBAO_MAICAI = 800; // 淘宝买菜

    // 青云商品经营品类Code
    const CATEGORY_FOOD = 100;                    // 美食
    const CATEGORY_FRESH = 104;                   // 生鲜果蔬
    const CATEGORY_MEDICINE = 108;                // 医药健康
    const CATEGORY_SUPERMARKET = 105;             // 超市百货
    const CATEGORY_FLOWER = 103;                  // 鲜花绿植
    const CATEGORY_CAKE = 102;                    // 烘焙蛋糕
    const CATEGORY_DRINK = 101;                   // 饮品奶茶
    const CATEGORY_OTHER = 999;                   // 其他

    // 青云接口返回结果Code
    const RESULT_SUCCESS = 0;                   // 成功
    const RESULT_SYSTEM_ERROR = 1;              // 系统异常
    const RESULT_PARAM_ERROR = 2;               // 缺少参数，或参数格式错误
    const RESULT_SIGN_ERROR = 3;                // 签名验证失败
    const RESULT_UNAUTHORIZED = 4;              // 未授权或授权过期
    const RESULT_RATE_LIMIT = 5;                // 接口流控
    const RESULT_OTHER_ERROR = 9;               // 其他原因
    const RESULT_NO_CAPACITY = 10;              // 该地区暂无运力

    // 订单相关错误码
    const RESULT_ORDER_NOT_EXIST = 101;         // 订单不存在
    const RESULT_ORDER_COMPLETED = 102;         // 订单已完成，不能取消
    const RESULT_SERVICE_NOT_OPEN = 103;        // 门店未开通所选服务产品
    const RESULT_OUT_OF_RANGE = 104;            // 送货地址超出配送范围
    const RESULT_NO_APPOINTMENT = 105;          // 预约时间内无法完成履约
    const RESULT_NO_RIDER = 106;                // 运力紧张，无法创建订单
    const RESULT_INSUFFICIENT_BALANCE = 107;    // 账户余额不足或扣款失败
    const RESULT_NO_APPOINTMENT_SUPPORT = 108;  // 暂不支持预约单
    const RESULT_NO_REVERSE_ORDER = 109;        // 暂不支持逆向发单
    const RESULT_RIDER_ACCEPTED = 110;          // 骑手已接单，无法添加小费
    const RESULT_TIP_LIMIT = 111;               // 小费金额已至上限，无法继续添加
    const RESULT_NO_RIDER_LOCATION = 112;       // 骑手目前没有位置信息，请稍后重试

    // 回调相关错误码
    const RESULT_CARRIER_MISMATCH = 301;        // 配送商不匹配
    const RESULT_ORDER_NO_MISMATCH = 302;       // 配送商物流单号不匹配
    const RESULT_WAYBILL_NOT_EXIST = 303;       // 运单不存在

    // 骑手相关错误码
    const RESULT_RIDER_INFO_ERROR = 501;        // 骑手信息错误，请扫正确的骑手码
    const RESULT_RIDER_ORDER_LIMIT = 502;       // 骑手接单数已超上限
    const RESULT_RIDER_CANNOT_ACCEPT = 503;     // 骑手无法接单
    const RESULT_FUNCTION_NOT_OPEN = 504;       // 请联系配送商客服开通功能后使用

    // 门店相关错误码
    const RESULT_STORE_CREATE_FAILED = 601;     // 暂时无法创建门店或修改门店信息，需要稍后重试
    const RESULT_STORE_NO_CHANGE = 602;         // 此次变更未修改任何门店信息

    // 青云订单状态Code - 配送商订单状态（新枚举）
    const QY_STATUS_CREATED = 10;               // 已创建(待接单)
    const QY_STATUS_ACCEPTED = 20;              // 骑手已接单
    const QY_STATUS_ARRIVED_STORE = 25;         // 已到店
    const QY_STATUS_PICKED_UP = 30;             // 已取货
    const QY_STATUS_DELIVERED = 50;             // 已送达
    const QY_STATUS_CANCELLED = 99;             // 已取消

    // 系统订单状态到青云状态的映射
    const STATUS_TO_QY_MAP = [
        O2oErrandOrder::STATUS_WAITING_PAY => self::QY_STATUS_CREATED,      // 待支付 -> 已创建(待接单)
        O2oErrandOrder::STATUS_PAID => self::QY_STATUS_CREATED,             // 待接单 -> 已创建(待接单)
        O2oErrandOrder::STATUS_PICKUP => self::QY_STATUS_ACCEPTED,          // 待取货 -> 骑手已接单
        O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT => self::QY_STATUS_ARRIVED_STORE, // 到达取货点 -> 已到店
        O2oErrandOrder::STATUS_DELIVERY => self::QY_STATUS_PICKED_UP,       // 派送中 -> 已取货
        O2oErrandOrder::STATUS_FINISH => self::QY_STATUS_DELIVERED,         // 已完成 -> 已送达
        O2oErrandOrder::STATUS_CANCEL => self::QY_STATUS_CANCELLED,         // 已取消 -> 已取消
    ];

    protected string $developerId;
    protected string $secret;

    public function __construct()
    {

        // 青云调用协议配置
        $this->developerId = config('qingyun.developer_id', 'qingyun_developer_id');
        $this->secret = config('qingyun.secret', 'qingyun_secret');
    }



    /**
     * 查找已存在的商家
     *
     * @param array $data
     * @return Merchant|null
     */
    private function findExistingMerchant(array $data): ?Merchant
    {
        return Merchant::where('merchant_type', 'qingyun')
            ->where('phone', $data['contactPhone'])
            ->first();
    }

    /**
     * 创建新门店
     *
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function createNewStore(array $data): array
    {
        // 检查该手机号是否已有用户账号
        $user = User::where('phone', $data['contactPhone'])->first();

        // 如果没有用户账号，则创建一个
        if (!$user) {
            $userService = app(UserService::class);
            $user = $userService->registerUser(
                $data['contactPhone'],
                Hash::make('123456'), // 默认密码
                '', // 空邀请码
                \App\Models\SystemConfig::PlatformPT // 平台标识为跑腿平台
            );

            Log::channel('qingyun')->info("为青云商家创建关联用户账号成功", [
                'user_id' => $user->id,
                'phone' => $data['contactPhone']
            ]);
        }

        // 解析地址信息（从shopAddress中提取省市区信息）
        $addressInfo = $this->parseAddress($data['shopAddress']);

        // 创建商家账户
        $merchant = Merchant::create([
            'shop_name' => $data['shopName'],
            'phone' => $data['contactPhone'],
            'password' => Hash::make('123456'),
            'province' => $addressInfo['province_code'],
            'city' => $addressInfo['city_code'],
            'district' => $addressInfo['district_code'],
            'city_code' => $addressInfo['city_code'] ?? $addressInfo['district_code'], // 兼容旧字段，优先使用区县编码
            'address' => $data['shopAddress'],
            'contact_name' => '',
            'email' => '',
            'merchant_type' => 'qingyun',
            'status' => 1, // 青云商家默认审核通过
            'balance' => 0,
            'user_id' => $user->id,
        ]);

        // 生成配送商门店ID
        $carrierShopId = 'QY_' . $merchant->id;

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $user->id,
            'carrier_shop_id' => $carrierShopId,
            'operation' => 'create'
        ];
    }

    /**
     * 更新现有门店
     *
     * @param Merchant $merchant
     * @param array $data
     * @return array
     */
    private function updateExistingStore(Merchant $merchant, array $data): array
    {
        // 解析地址信息
        $addressInfo = $this->parseAddress($data['shopAddress']);

        // 更新商家信息
        $merchant->update([
            'shop_name' => $data['shopName'],
            'province' => $addressInfo['province_code'],
            'city' => $addressInfo['city_code'],
            'district' => $addressInfo['district_code'],
            'city_code' => $addressInfo['city_code'] ?? $addressInfo['district_code'], // 兼容旧字段，优先使用区县编码
            'address' => $data['shopAddress'],
        ]);


        // 生成配送商门店ID
        $carrierShopId = 'QY_' . $merchant->id;

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $merchant->user_id,
            'carrier_shop_id' => $carrierShopId,
            'operation' => 'update'
        ];
    }


    /**
     * 解析地址信息，从详细地址中提取省市区信息
     * 参考 maiyatian 实现，返回地区名称和编码
     *
     * @param string $address
     * @return array
     */
    private function parseAddress(string $address): array
    {
        Log::channel('qingyun')->info('开始解析地址', ['address' => $address]);

        $province = '';
        $city = '';
        $district = '';
        $provinceCode = '';
        $cityCode = '';
        $districtCode = '';

        // 常见的省份匹配
        if (preg_match('/(北京|天津|上海|重庆)/', $address, $matches)) {
            $province = $matches[1] . '市';
            $city = $matches[1] . '市';
        } elseif (preg_match('/(.+?省)(.+?市)(.+?[区县])/', $address, $matches)) {
            $province = $matches[1];
            $city = $matches[2];
            $district = $matches[3];
        } elseif (preg_match('/(.+?市)(.+?[区县])/', $address, $matches)) {
            // 处理直辖市的情况
            $city = $matches[1];
            $district = $matches[2];
            if (in_array(substr($city, 0, 2), ['北京', '天津', '上海', '重庆'])) {
                $province = $city;
            }
        }

        // 通过地区名称查找对应的编码
        $regionCodes = $this->findRegionCodesByNames($province, $city, $district);

        // 如果解析失败，使用默认值（浙江省杭州市余杭区）
        if (empty($regionCodes['province']) || empty($regionCodes['city']) || empty($regionCodes['district'])) {
            Log::channel('qingyun')->warning('地址解析失败，使用默认地区', [
                'original_address' => $address,
                'parsed_province' => $province,
                'parsed_city' => $city,
                'parsed_district' => $district
            ]);

            $defaultRegions = $this->getDefaultRegions();
            $province = $defaultRegions['province']['name'];
            $city = $defaultRegions['city']['name'];
            $district = $defaultRegions['district']['name'];
            $provinceCode = $defaultRegions['province']['code'];
            $cityCode = $defaultRegions['city']['code'];
            $districtCode = $defaultRegions['district']['code'];
        } else {
            $provinceCode = $regionCodes['province']['code'] ?? '';
            $cityCode = $regionCodes['city']['code'] ?? '';
            $districtCode = $regionCodes['district']['code'] ?? '';

            // 更新名称为数据库中的标准名称
            $province = $regionCodes['province']['name'] ?? $province;
            $city = $regionCodes['city']['name'] ?? $city;
            $district = $regionCodes['district']['name'] ?? $district;
        }

        $result = [
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'province_code' => $provinceCode,
            'city_code' => $cityCode,
            'district_code' => $districtCode
        ];

        Log::channel('qingyun')->info('地址解析完成', $result);

        return $result;
    }

    /**
     * 根据地区名称查找对应的地区代码
     * 优化版本：一次查询获取所有匹配的地区信息
     *
     * @param string $provinceName 省份名称
     * @param string $cityName 城市名称
     * @param string $districtName 区县名称
     * @return array
     */
    private function findRegionCodesByNames(string $provinceName, string $cityName, string $districtName): array
    {
        $result = [
            'province' => null,
            'city' => null,
            'district' => null
        ];

        try {
            // 构建查询条件数组
            $searchConditions = [];

            if (!empty($provinceName)) {
                $searchConditions[] = ['name', 'like', "%{$provinceName}%", 'level' => 'province'];
            }
            if (!empty($cityName)) {
                $searchConditions[] = ['name', 'like', "%{$cityName}%", 'level' => 'city'];
            }
            if (!empty($districtName)) {
                $searchConditions[] = ['name', 'like', "%{$districtName}%", 'level' => 'district'];
            }

            if (empty($searchConditions)) {
                return $result;
            }

            // 一次查询获取所有匹配的地区
            $query = Region::query();
            $query->where(function($q) use ($searchConditions, $provinceName, $cityName, $districtName) {
                if (!empty($provinceName)) {
                    $q->orWhere(function($subQ) use ($provinceName) {
                        $subQ->where('name', 'like', "%{$provinceName}%")
                             ->where('level', 'province');
                    });
                }
                if (!empty($cityName)) {
                    $q->orWhere(function($subQ) use ($cityName) {
                        $subQ->where('name', 'like', "%{$cityName}%")
                             ->where('level', 'city');
                    });
                }
                if (!empty($districtName)) {
                    $q->orWhere(function($subQ) use ($districtName) {
                        $subQ->where('name', 'like', "%{$districtName}%")
                             ->where('level', 'district');
                    });
                }
            });

            $regions = $query->get();

            // 按级别分组处理结果
            foreach ($regions as $region) {
                switch ($region->level) {
                    case 'province':
                        if (!empty($provinceName) && stripos($region->name, $provinceName) !== false) {
                            $result['province'] = [
                                'name' => $region->name,
                                'code' => $region->code
                            ];
                        }
                        break;
                    case 'city':
                        if (!empty($cityName) && stripos($region->name, $cityName) !== false) {
                            $result['city'] = [
                                'name' => $region->name,
                                'code' => $region->code
                            ];
                        }
                        break;
                    case 'district':
                        if (!empty($districtName) && stripos($region->name, $districtName) !== false) {
                            $result['district'] = [
                                'name' => $region->name,
                                'code' => $region->code
                            ];
                        }
                        break;
                }
            }

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('查找地区代码失败', [
                'province' => $provinceName,
                'city' => $cityName,
                'district' => $districtName,
                'error' => $e->getMessage()
            ]);
        }

        return $result;
    }

    /**
     * 获取默认地区信息（浙江省杭州市余杭区）
     * 参考 maiyatian 实现
     *
     * @return array
     */
    private function getDefaultRegions(): array
    {
        try {
            $defaultProvince = Region::query()->where('name', '浙江省')->where('level', 'province')->first();
            $defaultCity = Region::query()->where('name', '杭州市')->where('level', 'city')->first();
            $defaultDistrict = Region::query()->where('name', '余杭区')->where('level', 'district')->first();

            if ($defaultProvince && $defaultCity && $defaultDistrict) {
                return [
                    'province' => [
                        'name' => $defaultProvince->name,
                        'code' => $defaultProvince->code
                    ],
                    'city' => [
                        'name' => $defaultCity->name,
                        'code' => $defaultCity->code
                    ],
                    'district' => [
                        'name' => $defaultDistrict->name,
                        'code' => $defaultDistrict->code
                    ]
                ];
            }
        } catch (\Exception $e) {
            Log::channel('qingyun')->error('获取默认地区信息失败', ['error' => $e->getMessage()]);
        }

        // 如果数据库查询失败，返回硬编码的默认值
        return [
            'province' => ['name' => '浙江省', 'code' => '330000'],
            'city' => ['name' => '杭州市', 'code' => '330100'],
            'district' => ['name' => '余杭区', 'code' => '330110']
        ];
    }

    /**
     * 获取城市编码（兼容旧版本，现在推荐使用 findRegionCodesByNames）
     *
     * @param string $cityName
     * @return string
     */
    private function getCityCode(string $cityName): string
    {
        // 尝试从数据库查找
        try {
            $city = Region::query()
                ->where('name', 'like', "%{$cityName}%")
                ->where('level', 'city')
                ->first();

            if ($city) {
                return $city->code;
            }
        } catch (\Exception $e) {
            Log::channel('qingyun')->warning('从数据库获取城市编码失败', [
                'city_name' => $cityName,
                'error' => $e->getMessage()
            ]);
        }

        // 备用硬编码映射
        $cityCodes = [
            '北京市' => '110100',
            '天津市' => '120100',
            '上海市' => '310100',
            '重庆市' => '500100',
            '杭州市' => '330100',
            '广州市' => '440100',
            '深圳市' => '440300',
            '成都市' => '510100',
            '武汉市' => '420100',
            '西安市' => '610100',
        ];

        return $cityCodes[$cityName] ?? '330100'; // 默认杭州
    }


    /**
     * 询价接口 - 按照 maiyatian 模式实现，支持已有订单查询
     *
     * @param array $data 询价请求数据
     * @return array 询价结果
     * @throws \Exception
     */
    public function valuatingWithOrderLookup(array $data): array
    {

        try {
            // 1. 查找用户ID
            $merchant = $this->findByCarrierMerchantId($data['carrierMerchantId']);
            if (!$merchant) {
                return [
                    'code' => self::RESULT_PARAM_ERROR,
                    'message' => '配送商ID不存在',
                    'data' => null
                ];
            }

            $userId = $merchant->user_id;

            // 2. 查找是否已存在订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();
            // 3. 如果订单已存在，返回订单信息
            if ($order) {
                $result = $this->formatExistingOrderResult($order);

                return [
                    'code' => self::RESULT_SUCCESS,
                    'message' => '成功',
                    'data' => $result
                ];
            }

            // 4. 如果订单不存在，使用 O2oErrandOrderService 进行询价
            $result = $this->performNewOrderValuating($userId, $data);


            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $result
            ];

        } catch (\Exception $e) {


            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 根据配送商ID查找配送商
     *
     * @param string $carrierMerchantId
     * @return int|null
     */
    private function findByCarrierMerchantId(string $carrierMerchantId)
    {
        $merchant = null;
        if (strpos($carrierMerchantId, 'QY_') === 0) {
            $merchantId = substr($carrierMerchantId, 3);
            $merchant = Merchant::where('merchant_type', 'qingyun')
                ->where('id', $merchantId)
                ->first();
        }

        return $merchant;
    }

    /**
     * 格式化已有订单的返回结果
     *
     * @param O2oErrandOrder $order
     * @return array
     */
    private function formatExistingOrderResult(O2oErrandOrder $order): array
    {
        // 计算实际支付金额（与 performNewOrderValuating 逻辑保持一致）
        $actualFee = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));

        return [
            'predictDeliveryTime' => $order->estimated_delivery_time->timestamp,
            'actualFee' => $actualFee,
            'deliveryFee' => $actualFee,
            'deliveryDistance' => $order->distance,
            'discountFee' => 0.0,
            'insuredFee' => 0.0,
        ];
    }

    /**
     * 执行新订单询价
     *
     * @param int $userId
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function performNewOrderValuating(int $userId, array $data): array
    {
        $service = new O2oErrandOrderService();

        // 转换经纬度格式（青云传入的是整数，需要转换为小数）
        $senderLng = $data['senderLng'] / 1000000;
        $senderLat = $data['senderLat'] / 1000000;
        $recipientLng = $data['recipientLng'] / 1000000;
        $recipientLat = $data['recipientLat'] / 1000000;

        // 准备预约时间
        $appointmentTime = "NOW";
        if ($data['prebook'] == 1) {
            $expectedTime = 0;
            if (isset($data['expectedDeliveryTime']) && $data['expectedDeliveryTime'] > 0) {
                $expectedTime = $data['expectedDeliveryTime'];
            } elseif (isset($data['expectedLeftDeliveryTime']) && $data['expectedLeftDeliveryTime'] > 0) { // 如果预约时间是时间段，则取左区间
                $expectedTime = $data["expectedLeftDeliveryTime"];
            }
            if ($expectedTime > 0) {
                $res = (new GaodeService())->electrobike(strval($senderLng), strval($senderLat), strval($recipientLng), strval($recipientLat));
                $paths = $res["route"]["paths"] ?? [[]];
                $index = 0;
                if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) == 2) {
                    $index = count($paths) - 1;
                }
                $needSeconds = $service->getSeconds(intval($paths[$index]["duration"] ?? 0));
                $appointmentTime = date("Y-m-d H:i:s", $expectedTime - $needSeconds) . "|" . date("Y-m-d H:i:s", $expectedTime);
            }
        }

        // 调用 O2oErrandOrderService 的 preOrder 方法
        $preOrderParams = [
            "type" => 1, // 普通配送
            "coupon_id" => 0,
            "gratuity" => 0, // 青云暂不支持小费
            "appointment_time" => $appointmentTime,
            "goods_info" => [
                "is_protect_price" => isset($data['insuredMark']) && $data['insuredMark'] == 1,
                "price" => isset($data['totalValue']) ? $data['totalValue'] : 0,
                "weight" => $data['totalWeight']/1000,
            ],
            "start_point" => [
                "mode" => 1, // 指定地址
                "lng" => $senderLng,
                "lat" => $senderLat,
            ],
            "end_point" => [
                "address_id" => 0, // 青云不使用地址簿
                "lng" => $recipientLng,
                "lat" => $recipientLat,
            ],
        ];

        $res = $service->preOrder($userId, $preOrderParams);
        return [
            'predictDeliveryTime' => strtotime($res["normal"]["estimated_delivery_time"]),
            'actualFee' => floatval($res["normal"]["total_amount"]),
            'deliveryFee' => floatval($res["normal"]["total_amount"]), // 青云的 deliveryFee 等于 actualFee（不含保价费）
            'deliveryDistance' => intval($res['distance']),
            'discountFee' => 0.00, // 暂不支持优惠
            'insuredFee' => 0.00, // 保价费在 actualFee 中已包含
        ];
    }

    /**
     * 发单接口 - 创建配送订单
     *
     * @param array $data 发单请求数据
     * @return array 发单结果
     * @throws \Exception
     */
    public function send(array $data): array
    {
        Log::channel('qingyun')->info('青云发单请求处理开始', $data);

        try {
            // 1. 查找用户ID
            $merchant = $this->findByCarrierMerchantId($data['carrierMerchantId']);
            if (!$merchant) {
                return [
                    'code' => self::RESULT_PARAM_ERROR,
                    'message' => '配送商ID不存在',
                    'data' => null
                ];
            }

            $userId = $merchant->user_id;
            $merchantId = $merchant->id;


            // 3. 准备预约时间
            $appointmentTime = $this->prepareAppointmentTime($data);

            // 4. 准备地址信息
            $addressInfo = $this->prepareAddressInfo($userId, $data);

            // 5. 创建订单
            $order = $this->createOrderWithPreparedData(
                $userId,
                $data,
                $appointmentTime,
                $addressInfo['startAddress'],
                $addressInfo['endAddress'],
                $merchantId
            );

            // 6. 格式化返回结果
            $result = $this->formatOrderResultData($order);

            Log::channel('qingyun')->info('青云发单成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'result' => $result
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云发单失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 准备预约时间
     *
     * @param array $data
     * @return string
     */
    private function prepareAppointmentTime(array $data): string
    {
        if ($data['prebook'] == 0) {
            // 即时单
            return "NOW";
        }

        // 预约单 - 需要计算合适的取货时间
        $expectedTime = 0;
        if (isset($data['expectedDeliveryTime']) && $data['expectedDeliveryTime'] > 0) {
            $expectedTime = $data['expectedDeliveryTime'];
        } elseif (isset($data['expectedLeftDeliveryTime']) && $data['expectedLeftDeliveryTime'] > 0) {
            $expectedTime = $data['expectedLeftDeliveryTime'];
        }

        if ($expectedTime > 0) {
            // 使用高德地图API计算配送时间，然后反推取货时间
            try {
                // 转换经纬度格式（青云传入的是整数，需要转换为小数）
                $senderLng = $data['senderLng'] / 1000000;
                $senderLat = $data['senderLat'] / 1000000;
                $recipientLng = $data['recipientLng'] / 1000000;
                $recipientLat = $data['recipientLat'] / 1000000;

                // 调用高德地图API计算路径
                $gaodeService = new GaodeService();
                $res = $gaodeService->electrobike(strval($senderLng),strval($senderLat), strval($recipientLng), strval($recipientLat));

                $paths = $res["route"]["paths"] ?? [[]];
                $index = 0;

                // 根据系统配置选择路径（最快或最慢）
                if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) == 2) {
                    $index = count($paths) - 1;
                }

                // 获取配送时长（秒）
                $duration = intval($paths[$index]["duration"] ?? 0);
                $service = new O2oErrandOrderService();
                $needSeconds = $service->getSeconds($duration);

                // 根据期望送达时间反推取货时间
                $pickupStartTime = $expectedTime - $needSeconds; // 提前30分钟开始取货
                $pickupEndTime = $expectedTime;

                $startTime = date("Y-m-d H:i:s", $pickupStartTime);
                $endTime = date("Y-m-d H:i:s", $pickupEndTime);

                return $startTime . "|" . $endTime;

            } catch (\Exception $e) {
                Log::channel('qingyun')->warning('计算预约时间失败，使用默认逻辑', [
                    'error' => $e->getMessage(),
                    'data' => $data
                ]);

                // 如果计算失败，使用简单的30分钟提前逻辑
                $startTime = date("Y-m-d H:i:s", $expectedTime - 30 * 60);
                $endTime = date("Y-m-d H:i:s", $expectedTime);
                return $startTime . "|" . $endTime;
            }
        }

        return "NOW";
    }

    /**
     * 准备地址信息
     *
     * @param int $userId
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function prepareAddressInfo(int $userId, array $data): array
    {
        // 转换经纬度格式（青云传入的是整数，需要转换为小数）
        $senderLng = $data['senderLng'] / 1000000;
        $senderLat = $data['senderLat'] / 1000000;
        $recipientLng = $data['recipientLng'] / 1000000;
        $recipientLat = $data['recipientLat'] / 1000000;

        // 创建发件人地址
        $startAddress = $this->createUserAddress($userId, [
            'name' => $data['senderName'],
            'tel' => $data['senderContract'],
            'address_detail' => $data['senderAddressDetail'],
            'longitude' => $senderLng,
            'latitude' => $senderLat,
            'type' => 'sender'
        ]);

        // 创建收件人地址
        $endAddress = $this->createUserAddress($userId, [
            'name' => $data['recipientName'],
            'tel' => $data['recipientPhone'],
            'address_detail' => $data['recipientAddress'],
            'longitude' => $recipientLng,
            'latitude' => $recipientLat,
            'type' => 'recipient'
        ]);

        return [
            'startAddress' => $startAddress,
            'endAddress' => $endAddress
        ];
    }

    /**
     * 创建用户地址
     *
     * @param int $userId
     * @param array $addressData
     * @return UserAddress
     * @throws \Exception
     */
    private function createUserAddress(int $userId, array $addressData): UserAddress
    {
        // 解析地址信息
        $addressInfo = $this->parseAddress($addressData['address_detail']);

        // 查找已存在的地址
        $address = UserAddress::query()->where("tel", $addressData['tel'])
            ->where("province", $addressInfo['province'])
            ->where("city", $addressInfo['city'])
            ->where("county", $addressInfo['district'])
            ->where("address_detail", $addressData['address_detail'])
            ->where('user_id', $userId)
            ->first();

        if (!$address) {
            $address = UserAddress::create([
                'user_id' => $userId,
                'name' => $addressData['name'],
                'tel' => $addressData['tel'],
                'province' => $addressInfo['province'],
                'city' => $addressInfo['city'],
                'county' => $addressInfo['district'],
                'address_detail' => $addressData['address_detail'],
                'address_remark' => '',
                'area_code' => $addressInfo['district_code'] ?? $addressInfo['city_code'] ?? '', // 使用区县编码或城市编码
                'longitude' => $addressData['longitude'],
                'latitude' => $addressData['latitude'],
                'is_default' => 0,
            ]);
        }

        return $address;
    }

    /**
     * 使用准备好的数据创建订单
     *
     * @param int $userId
     * @param array $data
     * @param string $appointmentTime
     * @param UserAddress $startAddress
     * @param UserAddress $endAddress
     * @param int $merchantId
     * @return O2oErrandOrder
     * @throws \Exception
     */
    private function createOrderWithPreparedData(
        int $userId,
        array $data,
        string $appointmentTime,
        UserAddress $startAddress,
        UserAddress $endAddress,
        int $merchantId
    ): O2oErrandOrder {
        $service = new O2oErrandOrderService();

        // 商品分类映射表（青云分类到系统分类）
        $goodsCategoryMap = $this->getGoodsCategoryMap();

        // 构建订单标题
        $title = $this->buildOrderTitle($data);

        // 构建订单备注
        $remark = $this->buildOrderRemark($data);

        // 解析商品详情
        $goodsDesc = $this->parseGoodsDetails($data);

        $orderParams = [
            "appointment_time" => $appointmentTime,
            "type" => O2oErrandOrder::TYPE_SEND,
            "coupon_id" => 0,
            "gratuity" => $data["tipFee"] ?? 0,
            "out_order_no" => $data["orderId"],
            "app_key" => O2oErrandOrder::APP_KEY_QY,
            'paid_at' => Carbon::now(),
            'pay_method' => Common::PAY_METHOD_OUT_SYSTEM,
            "order_status"=> O2oErrandOrder::STATUS_PAID,
            "title" => $title,
            "remark" => $remark,
            "hide_address" => false,
            "is_special" => false,
            "need_incubator" => false,
            "goods_info" => [
                "price" => $data["totalValue"] ?? 0,
                "is_protect_price" => isset($data['insuredMark']) && $data['insuredMark'] == 1,
                "desc" => $goodsDesc,
                "imgs" => [],
                "goods_category_id" => $goodsCategoryMap[$data["category"] ?? ""] ?? 14,
                "category_id" => 0,
                "weight" => $data["totalWeight"] ?$data["totalWeight"]/1000:0,
                "volume" => $data["totalVolume"] ?? 0,
            ],
            "start_point" => [
                "mode" => 0,
                "address_id" => $startAddress->id,
                "pickup_code" => false,
                "pickup_code_mode" => 0,
            ],
            "end_point" => [
                "address_id" => $endAddress->id,
                "receive_code" => false,
                "receive_code_mode" => 0,
            ],
        ];

        return $service->createOrder($userId, $orderParams, true, $merchantId);
    }

    /**
     * 获取商品分类映射表
     *
     * @return array
     */
    private function getGoodsCategoryMap(): array
    {
        return [

            // 分类映射成和麦芽田一致
            self::CATEGORY_FOOD => 5,        // 餐饮美食
            self::CATEGORY_FRESH => 7,       // 生鲜果蔬
            self::CATEGORY_MEDICINE => 14,    // 医药健康
            self::CATEGORY_SUPERMARKET => 14, // 超市百货
            self::CATEGORY_FLOWER => 9,      // 鲜花绿植
            self::CATEGORY_CAKE => 8,        // 烘焙蛋糕
            self::CATEGORY_DRINK => 5,       // 饮品奶茶
            self::CATEGORY_OTHER => 14,      // 其他
        ];
    }

    /**
     * 构建订单标题
     *
     * @param array $data
     * @return string
     */
    private function buildOrderTitle(array $data): string
    {
        $sourceMap = [
            self::TRADE_ORDER_SOURCE_JINGDONG => '青云｜京东',
            self::TRADE_ORDER_SOURCE_MEITUAN => '青云｜美团',
            self::TRADE_ORDER_SOURCE_ELEME => '青云｜饿了么',
            self::TRADE_ORDER_SOURCE_QINGYUN => '青云自营',
        ];

        $sourceName = $sourceMap[$data['tradeOrderSource']] ?? '青云';
        $orderSequence = $data['orderSequence'] ?? '';

        if ($orderSequence) {
            return $sourceName . '#' . $orderSequence;
        }

        return $sourceName . '订单';
    }

    /**
     * 构建订单备注
     *
     * @param array $data
     * @return string
     */
    private function buildOrderRemark(array $data): string
    {
        $remarkParts = [];

        if (!empty($data['orderRemark'])) {
            $remarkParts[] = '订单备注：' . $data['orderRemark'];
        }

        if (!empty($data['shopRemark'])) {
            $remarkParts[] = '商户备注：' . $data['shopRemark'];
        }

        if (!empty($data['tradeOrderId'])) {
            $remarkParts[] = '渠道订单号：' . $data['tradeOrderId'];
        }

        return implode(' | ', $remarkParts);
    }

    /**
     * 解析商品详情
     *
     * @param array $data
     * @return string
     */
    private function parseGoodsDetails(array $data): string
    {
        if (empty($data['goodsDetails'])) {
            return '商品';
        }

        try {
            $goodsList = json_decode($data['goodsDetails'], true);
            if (is_array($goodsList) && !empty($goodsList)) {
                $firstGoods = $goodsList[0];
                $name = $firstGoods['name'] ?? '商品';
                $count = count($goodsList);
                return $count > 1 ? $name . '等' . $count . '件商品' : $name;
            }
        } catch (\Exception $e) {
            Log::channel('qingyun')->warning('解析商品详情失败', [
                'goodsDetails' => $data['goodsDetails'],
                'error' => $e->getMessage()
            ]);
        }

        return '商品';
    }

    /**
     * 格式化订单结果数据
     *
     * @param O2oErrandOrder $order
     * @return array
     */
    public function formatOrderResultData(O2oErrandOrder $order): array
    {
        // 计算实际支付金额（分转元）
        $actualFee = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));

        // 配送费（不含保价费和小费）
        $deliveryFee = floatval(fentoyuan($order->actual_amount));

        // 小费金额
        $tipFee = floatval(fentoyuan($order->gratuity));

        // 保价费
        $insuredFee = floatval(fentoyuan($order->goods_protected_price));

        $result = [
            'carrierDeliveryId' => $order->order_no,
            'orderId' => $order->out_order_no,
            'predictDeliveryTime' => $order->estimated_delivery_time->timestamp,
            'actualFee' => $actualFee,
            'discountFee' => 0.0, // 暂不支持优惠
            'insuredFee' => $insuredFee,
            'deliveryFee' => $deliveryFee,
            'tipFee' => $tipFee,
            'deliveryDistance' => $order->distance,
        ];

        // 添加订单图片信息（当goods_imgs和buy_imgs不为空时）
        if (!empty($order->goods_imgs) || !empty($order->buy_imgs)) {
            $orderImages = $this->getOrderImages($order);
            if (!empty($orderImages)) {
                // 确保每个场景的URL总长度不超过1000字符
                foreach ($orderImages as $scene => $images) {
                    $totalLength = 0;
                    $validImages = [];
                    foreach ($images as $image) {
                        $imageLength = strlen($image);
                        if ($totalLength + $imageLength <= 1000) {
                            $validImages[] = $image;
                            $totalLength += $imageLength;
                        } else {
                            break; // 超过1000字符限制，停止添加
                        }
                    }
                    $orderImages[$scene] = $validImages;
                }

                // 只有当有有效图片时才添加orderImages字段
                if (!empty(array_filter($orderImages))) {
                    $result['orderImages'] = $orderImages;
                }
            }
        }

        return $result;
    }

    /**
     * 取消订单接口 - 参考maiyatian的取消配送实现
     *
     * @param array $data 取消订单请求数据
     * @return array 取消订单结果
     * @throws \Exception
     */
    public function cancelOrder(array $data): array
    {
        Log::channel('qingyun')->info('青云取消订单请求处理开始', $data);

        try {
            // 1. 查找订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();

            if (!$order) {
                return [
                    'code' => self::RESULT_ORDER_NOT_EXIST,
                    'message' => '订单不存在',
                    'data' => null
                ];
            }

            // 2. 检查订单是否已完成，已完成的订单不能取消
            if ($order->order_status == O2oErrandOrder::STATUS_FINISH) {
                Log::channel('qingyun')->warning('青云取消订单失败（订单已完成）', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'order_status' => $order->order_status
                ]);

                return [
                    'code' => self::RESULT_ORDER_COMPLETED,
                    'message' => '订单已完成，不能取消',
                    'data' => null
                ];
            }

            // 3. 检查订单是否已经取消（幂等性处理）
            if ($order->refund_status != O2oErrandOrder::REFUND_STATUS_INIT) {
                // 订单已经取消，返回取消费用信息（幂等性）
                $cancelFee = $this->calculateCancelFee($order);

                Log::channel('qingyun')->info('青云取消订单成功（订单已取消）', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'refund_status' => $order->refund_status,
                    'cancel_fee' => $cancelFee
                ]);

                return [
                    'code' => self::RESULT_SUCCESS,
                    'message' => '成功',
                    'data' => [
                        'cancelFee' => $cancelFee
                    ]
                ];
            }

            // 4. 构建取消原因 - 参考maiyatian的实现
            $cancelReason = $this->buildCancelReason($data);

            // 5. 调用 O2oErrandOrderService 的 refundOrder 方法进行退款 - 完全按照maiyatian的方式
            $orderService = new O2oErrandOrderService();
            $refundedOrder = $orderService->refundOrder($order->order_no, $cancelReason);

            $cancelFee = $this->calculateCancelFee($refundedOrder);

            Log::channel('qingyun')->info('青云取消订单成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'cancel_reason' => $cancelReason,
                'cancel_fee' => $cancelFee
            ]);

            // 6. 主动触发回调 - 参考qingyuncallback的实现
            try {
                // 获取青云状态码（取消状态）
                $qingyunStatus = $this->getQingyunStatus($refundedOrder);

                // 构建其他数据
                $otherData = [];
                if ($refundedOrder->rider_id && $refundedOrder->rider) {
                    $riderLocationService = app(\App\Services\RiderLocationService::class);
                    $location = $riderLocationService->getRiderLocation($refundedOrder->rider_id);

                    $otherData = [
                        "rider_name" => $refundedOrder->rider->name ?? "",
                        "rider_phone" => $refundedOrder->rider->phone ?? "",
                        "longitude" => $location ? $location['lng'] : "",
                        "latitude" => $location ? $location['lat'] : "",
                    ];
                }

                // 主动回传取消状态
                $callbackSuccess = $this->deliveryStatusCallback(
                    $refundedOrder->user_id,
                    $refundedOrder->order_no,
                    $refundedOrder->out_order_no,
                    $qingyunStatus,
                    $otherData
                );

                Log::channel('qingyun')->info('青云取消订单回调结果', [
                    'order_no' => $refundedOrder->order_no,
                    'callback_success' => $callbackSuccess
                ]);

            } catch (\Exception $callbackException) {
                // 回调失败不影响取消订单的主流程
                Log::channel('qingyun')->error('青云取消订单回调失败', [
                    'order_no' => $refundedOrder->order_no,
                    'error' => $callbackException->getMessage(),
                    'trace' => $callbackException->getTraceAsString()
                ]);
            }

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => [
                    'cancelFee' => $cancelFee
                ]
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云取消订单失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 构建取消原因 - 青云定义的结构
     *
     * @param array $data
     * @return string
     */
    private function buildCancelReason(array $data): string
    {
        // 如果有具体的取消原因说明，直接使用
        if (!empty($data['cancelReasonDesc'])) {
            return $data['cancelReasonDesc'];
        }

        // 根据青云定义的取消原因code映射
        $cancelReasonMap = [
            1 => "商户取消",
            2 => "用户在渠道或更上一层的取消",
            3 => "因为各系统原因发起的取消",
            4 => "配送商的骑手发起的取消",
            5 => "配送商侧发起的取消配送",
            99 => "其他原因", // 其他原因统一使用code 99
        ];

        return $cancelReasonMap[$data['cancelReasonCode']] ?? "其他原因";
    }

    /**
     * 根据订单的取消原因获取青云取消原因代码
     *
     * @param O2oErrandOrder $order
     * @return int
     */
    private function getCancelReasonCode(O2oErrandOrder $order): int
    {
        $closeReason = $order->close_reason ?? '';

        // 根据取消原因文本反向映射到代码
        $reasonToCodeMap = [
            "商户取消" => 1,
            "用户在渠道或更上一层的取消" => 2,
            "因为各系统原因发起的取消" => 3,
            "配送商的骑手发起的取消" => 4,
            "配送商侧发起的取消配送" => 5,
        ];

        foreach ($reasonToCodeMap as $reason => $code) {
            if (strpos($closeReason, $reason) !== false) {
                return $code;
            }
        }

        // 其他原因统一返回 99
        return 99;
    }

    /**
     * 计算取消扣费（违约金）- 基于实际支付金额和退款金额的差额
     *
     * @param O2oErrandOrder $order
     * @return float
     */
    private function calculateCancelFee(O2oErrandOrder $order): float
    {
        // 计算实际支付金额（分转元）
        $totalPayAmount = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));

        // 计算退款金额（分转元）
        $refundAmount = floatval(fentoyuan($order->refund_amount));

        // 取消扣费 = 实际支付金额 - 退款金额
        $cancelFee = $totalPayAmount - $refundAmount;

        // 确保取消扣费不为负数
        return max(0.0, round($cancelFee, 2));
    }

    /**
     * 获取订单详情接口
     *
     * @param array $data 订单详情请求数据
     * @return array 订单详情结果
     * @throws \Exception
     */
    public function getOrderDetail(array $data): array
    {
        Log::channel('qingyun')->info('青云订单详情请求处理开始', $data);

        try {
            // 1. 查找订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();

            if (!$order) {
                return [
                    'code' => self::RESULT_ORDER_NOT_EXIST,
                    'message' => '订单不存在',
                    'data' => null
                ];
            }

            // 2. 格式化订单详情数据
            $orderDetail = $this->formatOrderDetailData($order);

            Log::channel('qingyun')->info('青云订单详情查询成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'status' => $order->order_status
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $orderDetail
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云订单详情查询失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 格式化订单详情数据
     *
     * @param O2oErrandOrder $order
     * @return array
     */
    private function formatOrderDetailData(O2oErrandOrder $order): array
    {
        // 根据 refund_status 判断订单是否取消，而不是根据 order_status
        if ($order->refund_status != O2oErrandOrder::REFUND_STATUS_INIT || $order->order_status == O2oErrandOrder::STATUS_CANCEL) {
            // 订单已取消
            $qingyunStatus = self::QY_STATUS_CANCELLED;
        } else {
            // 获取青云状态码
            $qingyunStatus = self::STATUS_TO_QY_MAP[$order->order_status] ?? self::QY_STATUS_CREATED;
        }

        // 计算各种费用（分转元）
        $actualFee = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));
        $deliveryFee = floatval(fentoyuan($order->actual_amount));
        $tipFee = floatval(fentoyuan($order->gratuity));
        $insuredFee = floatval(fentoyuan($order->goods_protected_price));
        $discountFee = floatval(fentoyuan($order->coupon_amount));

        // 基础订单详情数据
        $orderDetail = [
            'orderId' => $order->out_order_no,
            'carrierDeliveryId' => $order->order_no,
            'status' => $qingyunStatus,
            'operateTime' => $order->updated_at->timestamp,
            'createOrderTime' => $order->create_time->timestamp,
            'actualFee' => $actualFee,
            'discountFee' => $discountFee,
            'insuredFee' => $insuredFee,
            'deliveryFee' => $deliveryFee,
            'deliveryDistance' => $order->distance,
        ];

        // 添加预计送达时间（如果有）
        if ($order->estimated_delivery_time) {
            $orderDetail['predictDeliveryTime'] = $order->estimated_delivery_time->timestamp;
        }

        // 添加小费金额（如果有）
        if ($tipFee > 0) {
            $orderDetail['tipFee'] = $tipFee;
        }

        // 添加骑手信息（如果已接单）
        if ($order->rider_id && $order->rider) {
            $orderDetail['riderPhone'] = $order->rider->phone ?? '';
            $orderDetail['riderName'] = $order->rider->name ?? '';
        }

        // 添加取消相关信息（根据 refund_status 判断订单是否取消）
        if ($order->refund_status != O2oErrandOrder::REFUND_STATUS_INIT || $order->order_status == O2oErrandOrder::STATUS_CANCEL) {
            $orderDetail['cancelReasonCode'] = $this->getCancelReasonCode($order);
            $orderDetail['cancelReasonDesc'] = $order->close_reason ?? '订单已取消';
            $orderDetail['cancelFee'] = $this->calculateCancelFee($order);
        }

        // 添加订单图片信息（当goods_imgs和buy_imgs不为空时）
        if (!empty($order->goods_imgs) || !empty($order->buy_imgs)) {
            $orderImages = $this->getOrderImages($order);
            if (!empty($orderImages)) {
                // 确保每个场景的URL总长度不超过1000字符
                foreach ($orderImages as $scene => $images) {
                    $totalLength = 0;
                    $validImages = [];
                    foreach ($images as $image) {
                        $imageLength = strlen($image);
                        if ($totalLength + $imageLength <= 1000) {
                            $validImages[] = $image;
                            $totalLength += $imageLength;
                        } else {
                            break; // 超过1000字符限制，停止添加
                        }
                    }
                    $orderImages[$scene] = $validImages;
                }

                // 只有当有有效图片时才添加orderImages字段
                if (!empty(array_filter($orderImages))) {
                    $orderDetail['orderImages'] = $orderImages;
                }
            }
        }

        return $orderDetail;
    }

    /**
     * 获取订单图片信息
     *
     * @param O2oErrandOrder $order
     * @return array
     */
    private function getOrderImages(O2oErrandOrder $order): array
    {
        $orderImages = [];

        // 获取取货照片
        if (!empty($order->goods_imgs)) {
            $pickupImages = [];
            foreach ($order->goods_imgs as $img) {
                if (!empty($img) && is_string($img)) {
                    $pickupImages[] = $img;
                }
            }
            if (!empty($pickupImages)) {
                $orderImages['pickup'] = array_slice($pickupImages, 0, 5); // 最多5张
            }
        }

        // 获取送达照片 - 这里需要根据实际的订单图片存储结构来获取
        // 如果有专门的送达照片字段，可以类似处理
        if (!empty($order->buy_imgs)) {
            $arrivedImages = [];
            foreach ($order->buy_imgs as $img) {
                if (!empty($img) && is_string($img)) {
                    $arrivedImages[] = $img;
                }
            }
            if (!empty($arrivedImages)) {
                $orderImages['arrived'] = array_slice($arrivedImages, 0, 5); // 最多5张
            }
        }

        return $orderImages;
    }

    /**
     * 骑手/司机经纬度查询接口 - 参考MaiYaTian的rider_location实现
     *
     * @param array $data 查询请求数据
     * @return array 查询结果
     * @throws \Exception
     */
    public function getRiderLocation(array $data): array
    {
        Log::channel('qingyun')->info('青云骑手位置查询请求处理开始', $data);

        try {
            // 1. 查找订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();

            if (!$order) {
                return [
                    'code' => self::RESULT_ORDER_NOT_EXIST,
                    'message' => '订单不存在',
                    'data' => null
                ];
            }

            // 2. 构建基础返回数据
            $result = [
                'orderId' => $order->out_order_no,
                'carrierDeliveryId' => $order->order_no,
                'riderLng' => 0,
                'riderLat' => 0,
            ];

            // 3. 如果订单有分配骑手且订单未完成，获取骑手位置
            if ($order->rider_id && $order->order_status < O2oErrandOrder::STATUS_FINISH) {
                // 使用RiderLocationService获取骑手位置 - 完全按照MaiYaTian的方式
                $riderLocationService = app(\App\Services\RiderLocationService::class);
                $location = $riderLocationService->getRiderLocation($order->rider_id);

                if ($location) {
                    // 转换坐标格式：青云要求坐标 * 10^6，火星坐标
                    $result['riderLng'] = intval($location['lng'] * 1000000);
                    $result['riderLat'] = intval($location['lat'] * 1000000);

                    Log::channel('qingyun')->info('青云骑手位置查询成功', [
                        'order_id' => $order->id,
                        'rider_id' => $order->rider_id,
                        'original_lng' => $location['lng'],
                        'original_lat' => $location['lat'],
                        'converted_lng' => $result['riderLng'],
                        'converted_lat' => $result['riderLat']
                    ]);
                } else {
                    Log::channel('qingyun')->warning('青云骑手位置查询：未获取到骑手位置', [
                        'order_id' => $order->id,
                        'rider_id' => $order->rider_id
                    ]);
                }
            } else {
                Log::channel('qingyun')->info('青云骑手位置查询：订单无骑手或已完成', [
                    'order_id' => $order->id,
                    'rider_id' => $order->rider_id,
                    'order_status' => $order->order_status
                ]);
                return [
                    "code" => self::RESULT_NO_RIDER_LOCATION,
                    "message" => "骑手目前没有位置信息，请稍后重试",
                ];
            }

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云骑手位置查询失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 添加小费接口
     *
     * @param array $data 请求数据
     * @return array 处理结果
     * @throws \Exception
     */
    public function addTip(array $data): array
    {
        try {
            Log::channel('qingyun')->info('青云添加小费处理开始', $data);

            $orderId = $data['orderId'];
            $tipFee = floatval($data['tipFee']);

            // 查找订单
            $order = O2oErrandOrder::where('out_order_no', $orderId)
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();

            if (!$order) {
                Log::channel('qingyun')->warning('青云订单不存在', ['order_id' => $orderId]);
                return [
                    'code' => self::RESULT_ORDER_NOT_EXIST,
                    'message' => '订单不存在',
                    'data' => null
                ];
            }

            // 将元转换为分进行处理（与maiyatian保持一致）
            $tipFeeInFen = yuantofen($tipFee);

            // 调用小费处理逻辑
            // 青云不在商户余额里充值，因此扣款走的是用户余额
            $this->processTips($order, $tipFeeInFen);

            // 按照青云接口规范返回数据
            $resultData = ['tipFee' => $tipFee];

            Log::channel('qingyun')->info('青云添加小费处理成功', [
                'order_id' => $orderId,
                'tip_fee' => $tipFee,
                'tip_fee_fen' => $tipFeeInFen
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $resultData
            ];

        } catch (BusinessException $e) {
            Log::channel('qingyun')->warning('青云添加小费业务异常', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            // 根据错误信息选择合适的错误码
            $errorCode = self::RESULT_PARAM_ERROR;
            if (strpos($e->getMessage(), '余额不足') !== false) {
                $errorCode = self::RESULT_INSUFFICIENT_BALANCE;
            } elseif (strpos($e->getMessage(), '已接单') !== false) {
                $errorCode = self::RESULT_RIDER_ACCEPTED;
            } elseif (strpos($e->getMessage(), '小费金额已至上限') !== false) {
                $errorCode = self::RESULT_TIP_LIMIT;
            }

            return [
                'code' => $errorCode,
                'message' => $e->getMessage(),
                'data' => null
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云添加小费系统异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 处理小费逻辑（参考maiyatian实现）
     *
     * @param O2oErrandOrder $order 订单
     * @param int $amount 小费金额（分）
     * @return array 处理结果
     * @throws \Exception
     */
    private function processTips(O2oErrandOrder $order, $amount): array
    {
        if ($amount <= 0) {
            return [];
        }

        // 基本状态检查
        if ($order->order_status == O2oErrandOrder::STATUS_CANCEL) {
            throw new \Exception("该订单已取消");
        }
        if ($order->order_status < O2oErrandOrder::STATUS_PAID || $order->order_status >= O2oErrandOrder::STATUS_FINISH) {
            throw new \Exception("该订单未支付或已完成");
        }

        // 判断是否为青云订单
        if ($order->app_key == O2oErrandOrder::APP_KEY_QY) {
            // 青云订单 - 从商家余额扣除
            return $this->processQingyunMerchantTips($order, $amount);
        } else {
            // 普通订单 - 从用户余额扣除
            return $this->processUserTips($order, $amount);
        }
    }

    /**
     * 处理青云商家小费扣费
     *
     * @param O2oErrandOrder $order 订单
     * @param int $amount 小费金额（元）
     * @return array 空数组
     * @throws BusinessException 当商家余额不足或处理失败时
     */
    private function processQingyunMerchantTips(O2oErrandOrder $order, $amount): array
    {
        try {
            // 获取青云商家信息
            $userId = $order->user_id ?? '';
            $merchant = Merchant::where('merchant_type',"qingyun")
                ->where('user_id', $userId)->first();

            if (!$merchant) {
                Log::channel($order->app_key)->warning('未找到商家信息，无法处理商家小费', [
                    'order_no' => $order->order_no,
                    'user_id' => $userId
                ]);
                throw new BusinessException("未找到商家信息，无法处理小费");
            }

            // 检查商家余额是否足够
            if ($amount > $merchant->balance) {
                throw new BusinessException("商家余额不足");
            }

            // 使用数据库原子操作更新余额
            $beforeBalance = $merchant->balance;
            $merchant->decrement('balance', $amount);

            // 记录商家账户变动（使用更新后的余额）
            $afterBalance = $merchant->fresh()->balance;
            MerchantAccountLog::create([
                'merchant_id' => $merchant->id,
                'amount' => -$amount,
                'before_balance' => $beforeBalance,
                'after_balance' => $afterBalance,
                'type' => MerchantAccountLog::TYPE_TIP,
                'order_no' => $order->order_no,
                'remark' => '青云订单加小费'
            ]);

            // 增加订单小费
            O2oErrandOrder::query()->where("id", $order->id)->increment("gratuity", $amount);

            Log::channel($order->app_key)->info('商家支付小费成功', [
                'merchant_id' => $merchant->id,
                'order_no' => $order->order_no,
                'amount' => $amount
            ]);

            return [];

        } catch (BusinessException $e) {
            // 业务异常直接向上抛出
            throw $e;
        } catch (\Exception $e) {
            // 其他异常记录日志并转为业务异常
            Log::channel($order->app_key)->error('商家扣款过程出错', [
                'order_no' => $order->order_no,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new BusinessException("商家扣款处理失败：" . $e->getMessage());
        }
    }

    /**
     * 处理从用户余额扣除小费
     *
     * @param O2oErrandOrder $order 订单
     * @param int $amount 小费金额
     * @return array 空数组
     * @throws BusinessException 当用户余额不足或处理失败时
     */
    private function processUserTips(O2oErrandOrder $order, $amount): array
    {
        try {
            // 获取用户账户
            $account = UserAccount::query()->firstOrCreate(['user_id' => $order->user_id]);

            // 检查用户余额是否足够
            if ($amount > $account->amount) {
                throw new BusinessException("用户余额不足");
            }

            // 单独事务处理用户扣款
            DB::transaction(function () use ($order, $account, $amount) {
                // 扣减用户余额
                $account->decrease($amount, UserAccountFlow::BUSINESS_TYPE_PAYMENT, $order->order_no, "订单加小费");

                // 增加订单小费
                O2oErrandOrder::query()->where("id", $order->id)->increment("gratuity", $amount);

                Log::channel($order->app_key)->info('用户支付小费成功', [
                    'user_id' => $account->user_id,
                    'order_no' => $order->order_no,
                    'amount' => $amount
                ]);
            });

            return [];

        } catch (BusinessException $e) {
            // 业务异常直接向上抛出
            throw $e;
        } catch (\Exception $e) {
            // 其他异常记录日志并转为业务异常
            Log::channel($order->app_key)->error('用户扣款过程出错', [
                'order_no' => $order->order_no,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new BusinessException("用户扣款处理失败：" . $e->getMessage());
        }
    }

    /**
     * 配送状态回调接口 - 向青云系统回调配送状态变更
     *
     * @param int $userId 用户ID
     * @param string $orderNo 配送商订单号
     * @param string $outOrderNo 青云平台订单号
     * @param int $status 青云状态码
     * @param array $otherData 其他数据（骑手信息、费用信息等）
     * @return bool 回调是否成功
     */
    public function deliveryStatusCallback(int $userId, string $orderNo, string $outOrderNo, int $status, array $otherData = []): bool
    {
        try {
            // 查找青云商户的回调配置
            $merchant = Merchant::where('user_id', $userId)
                ->where('merchant_type', 'qingyun')
                ->first();

            if (!$merchant) {
                Log::channel('qingyun')->warning('青云配送状态回调：未找到商户信息', [
                    'user_id' => $userId,
                    'order_no' => $orderNo,
                    'out_order_no' => $outOrderNo
                ]);
                return false;
            }

            // 查找订单信息
            $order = O2oErrandOrder::where('order_no', $orderNo)
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();

            if (!$order) {
                Log::channel('qingyun')->warning('青云配送状态回调：未找到订单信息', [
                    'order_no' => $orderNo,
                    'out_order_no' => $outOrderNo
                ]);
                return false;
            }

            // 构建回调数据
            $callbackData = $this->buildCallbackData($order, $status, $otherData);

            // 记录回调请求
            Log::info('青云配送状态回调请求', [
                'order_no' => $orderNo,
                'out_order_no' => $outOrderNo,
                'status' => $status,
                'callback_data' => $callbackData
            ]);

            // 发送回调请求到青云系统
            $success = $this->sendCallbackToQingyun($callbackData);

            if ($success) {
                Log::channel('qingyun')->info('青云配送状态回调成功', [
                    'order_no' => $orderNo,
                    'out_order_no' => $outOrderNo,
                    'status' => $status
                ]);
            } else {
                Log::channel('qingyun')->error('青云配送状态回调失败', [
                    'order_no' => $orderNo,
                    'out_order_no' => $outOrderNo,
                    'status' => $status
                ]);
            }

            return $success;

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云配送状态回调异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_no' => $orderNo,
                'out_order_no' => $outOrderNo,
                'status' => $status
            ]);

            return false;
        }
    }

    /**
     * 构建回调数据 - 完全按照青云接口规范
     *
     * @param O2oErrandOrder $order 订单信息
     * @param int $status 青云状态码
     * @param array $otherData 其他数据
     * @return array 回调数据
     */
    private function buildCallbackData(O2oErrandOrder $order, int $status, array $otherData): array
    {
        // 基础回调数据 - 必填字段
        $callbackData = [
            'orderId' => $order->out_order_no,                    // 青云平台订单号
            'carrierDeliveryId' => $order->order_no,              // 配送商物流单号
            'status' => $status,                                  // 运单状态
            'operateTime' => $order->updated_at->timestamp,       // 状态变更时间（时间戳）
        ];

        // 添加费用信息（分转元，保留两位小数）
        $callbackData['actualFee'] = round(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price), 2);

        // 添加其他费用信息（如果有）
        if ($order->gratuity > 0) {
            $callbackData['tipFee'] = round(fentoyuan($order->gratuity), 2);
        }

        // 添加骑手信息 - 在特定状态下必传
        $requireRiderInfo = in_array($status, [
            self::QY_STATUS_ACCEPTED,      // 已接单
            self::QY_STATUS_ARRIVED_STORE, // 已到店
            self::QY_STATUS_PICKED_UP,     // 已取货
            self::QY_STATUS_DELIVERED,     // 已送达
        ]);

        if ($requireRiderInfo && $order->rider_id && $order->rider) {
            $callbackData['riderName'] = $order->rider->name ?? '';
            $callbackData['riderPhone'] = $order->rider->phone ?? '';
            $callbackData['riderPhoneType'] = 0; // 0：真实号 1：隐私号

            // 添加骑手位置信息（火星坐标 * 10^6）
            if (isset($otherData['longitude']) && isset($otherData['latitude'])) {
                $callbackData['riderLng'] = intval($otherData['longitude'] * 1000000);
                $callbackData['riderLat'] = intval($otherData['latitude'] * 1000000);
            } else {
                // 实时查询骑手位置
                try {
                    $riderLocationService = app(\App\Services\RiderLocationService::class);
                    $location = $riderLocationService->getRiderLocation($order->rider_id);
                    if ($location) {
                        $callbackData['riderLng'] = intval($location['lng'] * 1000000);
                        $callbackData['riderLat'] = intval($location['lat'] * 1000000);
                    }
                } catch (\Exception $e) {
                    Log::channel('qingyun')->warning('获取骑手位置失败', [
                        'rider_id' => $order->rider_id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        // 添加取消相关信息（如果订单已取消）
        if ($status == self::QY_STATUS_CANCELLED) {
            $callbackData['cancelReasonCode'] = $this->getCancelReasonCode($order);
            $callbackData['cancelReasonDesc'] = $order->close_reason ?? '订单已取消';
            $callbackData['cancelFee'] = round($this->calculateCancelFee($order), 2);
        }

        // 添加订单图片信息（如果有）
        $orderImages = $this->getOrderImages($order);
        if (!empty($orderImages)) {
            $callbackData['orderImages'] = json_encode($orderImages);
        }

        return $callbackData;
    }

    /**
     * 发送回调请求到青云系统 - 按照青云调用协议
     *
     * @param array $callbackData 回调数据
     * @return bool 是否成功
     */
    private function sendCallbackToQingyun(array $callbackData): bool
    {
        try {
            // 青云回调地址 - 从配置文件读取
            $callbackUrl = config('qingyun.callback_url');

            // 如果没有配置回调地址，记录警告并返回成功（避免影响业务流程）
            if (empty($callbackUrl)) {
                Log::channel('qingyun')->warning('青云回调地址未配置，跳过回调', [
                    'callback_data' => $callbackData
                ]);
                return true;
            }

            // 构建青云协议公共参数
            $timestamp = time();
            $systemParams = [
                'developerId' => $this->developerId,
                'timestamp' => $timestamp,
                'version' => '1.0',
            ];

            // 合并系统参数和业务参数
            $allParams = array_merge($systemParams, $callbackData);

            // 生成签名
            $sign = $this->generateQingyunSignature($allParams);
            $allParams['sign'] = $sign;

            // 构建请求头 - 按照青云协议要求
            $headers = [
                'Content-Type' => 'application/x-www-form-urlencoded',
                'User-Agent' => 'RunningMan-Delivery-System/1.0',
            ];

            // 发送HTTP请求 - 使用 form 格式
            $response = \Illuminate\Support\Facades\Http::timeout(30)
                ->withHeaders($headers)
                ->asForm()
                ->post($callbackUrl, $allParams);

            log::info('青云回调响应', [
                'response' => $response->json(),
                'request_params' => $allParams
            ]);
            // 检查响应 - 按照青云接口规范
            if ($response->successful()) {
                $responseData = $response->json();

                // 青云响应格式：{"code": 0, "message": "成功", "data": null}
                // code = 0 表示成功，非0表示失败
                if (isset($responseData['code']) && $responseData['code'] === 0) {
                    Log::channel('qingyun')->info('青云回调成功', [
                        'response' => $responseData,
                        'request_params' => $allParams
                    ]);
                    return true;
                } else {
                    Log::channel('qingyun')->error('青云回调业务失败', [
                        'code' => $responseData['code'] ?? 'unknown',
                        'message' => $responseData['message'] ?? 'unknown error',
                        'response' => $responseData,
                        'request_params' => $allParams
                    ]);
                    return false;
                }
            } else {
                Log::error('青云回调HTTP请求失败', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'request_params' => $allParams
                ]);
                return false;
            }

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('发送青云回调异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'callback_data' => $callbackData
            ]);
            return false;
        }
    }

    /**
     * 生成青云签名 - 完全按照青云签名协议
     *
     * 青云签名规则：
     * 1）将所有系统参数及业务参数（其中sign，byte[]及值为空的参数除外）按照参数名的字典顺序排序
     * 2）将参数以参数1值1参数2值2...的顺序拼接，例如a=&c=3&b=1，变为b1c3，参数使用utf-8编码
     * 3）按照secret + 排序后的参数的顺序进行连接，得到加密前的字符串
     * 4）对加密前的字符串进行sha1加密并转为小写字符串，得到签名
     *
     * @param array $params 所有参数（系统参数+业务参数）
     * @return string 签名
     */
    private function generateQingyunSignature(array $params): string
    {
        // 1. 将所有系统参数及业务参数（其中sign，byte[]及值为空的参数除外）按照参数名的字典顺序排序
        $filteredParams = [];
        foreach ($params as $key => $value) {
            // 排除 sign 参数和空值参数（青云规则：值为空的参数除外）
            if ($key === 'sign' || $value === null || $value === '') {
                continue;
            }

            // 数组和对象转为JSON字符串（保持UTF-8编码）
            if (is_array($value) || is_object($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }

            // 布尔值转换为字符串（青云规则：boolean=true）
            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            }

            // 确保值为字符串类型
            $filteredParams[$key] = (string)$value;
        }

        // 按参数名字典顺序排序
        ksort($filteredParams);

        // 2. 将参数以参数1值1参数2值2...的顺序拼接
        // 例如：a=&c=3&b=1，变为b1c3
        $paramString = '';
        foreach ($filteredParams as $key => $value) {
            $paramString .= $key . $value;
        }

        // 3. 按照secret + 排序后的参数的顺序进行连接，得到加密前的字符串
        $signString = $this->secret . $paramString;

        // 调试日志：记录签名计算过程
        Log::channel('qingyun')->debug('青云回调签名计算过程', [
            'original_params' => $params,
            'filtered_params' => $filteredParams,
            'param_string' => $paramString,
            'full_sign_string' => $signString,
            'calculated_sign' => strtolower(sha1($signString))
        ]);

        // 4. 对加密前的字符串进行sha1加密并转为小写字符串，得到签名
        return strtolower(sha1($signString));
    }

    /**
     * 获取青云状态码
     * 将系统内部状态转换为青云状态码
     *
     * @param O2oErrandOrder $order 订单
     * @return int 青云状态码
     */
    public function getQingyunStatus(O2oErrandOrder $order): int
    {
        // 根据 refund_status 判断订单是否取消
        if ($order->refund_status != O2oErrandOrder::REFUND_STATUS_INIT) {
            return self::QY_STATUS_CANCELLED;
        }

        // 根据订单状态映射到青云状态
        return self::STATUS_TO_QY_MAP[$order->order_status] ?? self::QY_STATUS_CREATED;
    }

    /**
     * 测试青云签名生成 - 用于验证签名算法是否正确
     *
     * 根据青云文档示例：
     * secret: test
     * 系统参数：appkey=test timestamp=1477395862 version=1.0
     * 应用参数：number=123 string=测试 double=123.123 boolean=true empty=
     * 加密前字符串：testappkeytestbooleantruedouble123.123number123string测试timestamp1477395862version1.0
     * 期望签名：8943ba698f4b009f80dc2fd69ff9b313381263bd
     *
     * 注意：青云文档示例使用的是appkey，但实际API使用developerId
     *
     * @return array 测试结果
     */
    public function testQingyunSignature(): array
    {
        // 保存原始secret
        $originalSecret = $this->secret;

        // 使用测试secret
        $this->secret = 'test';

        // 构建测试参数（按照青云文档示例，使用appkey而不是developerId）
        $testParams = [
            // 系统参数（按照文档示例）
            'appkey' => 'test',
            'timestamp' => 1477395862,
            'version' => '1.0',
            // 应用参数
            'number' => 123,
            'string' => '测试',
            'double' => 123.123,
            'boolean' => true,
            'empty' => '', // 空值会被过滤掉
        ];

        // 生成签名
        $generatedSign = $this->generateQingyunSignature($testParams);
        $expectedSign = '8943ba698f4b009f80dc2fd69ff9b313381263bd';

        // 恢复原始secret
        $this->secret = $originalSecret;

        // 同时测试实际使用的developerId参数
        $this->secret = 'test';
        $actualParams = [
            // 实际使用的系统参数
            'developerId' => 'test',
            'timestamp' => 1477395862,
            'version' => '1.0',
            // 应用参数
            'number' => 123,
            'string' => '测试',
            'double' => 123.123,
            'boolean' => true,
            'empty' => '', // 空值会被过滤掉
        ];
        $actualSign = $this->generateQingyunSignature($actualParams);

        // 恢复原始secret
        $this->secret = $originalSecret;

        return [
            'doc_example' => [
                'test_params' => $testParams,
                'generated_sign' => $generatedSign,
                'expected_sign' => $expectedSign,
                'is_correct' => $generatedSign === $expectedSign,
            ],
            'actual_usage' => [
                'test_params' => $actualParams,
                'generated_sign' => $actualSign,
                'note' => '实际API使用developerId参数'
            ],
            'secret_used' => 'test'
        ];
    }
}
